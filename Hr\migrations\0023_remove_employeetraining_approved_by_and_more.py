# Generated by Django 5.0.14 on 2025-07-20 15:10

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Hr', '0022_remove_employeetraining_approved_by_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='employeetraining',
            name='approved_by',
        ),
        migrations.RemoveField(
            model_name='employeetraining',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='employeetraining',
            name='employee',
        ),
        migrations.AlterField(
            model_name='payrollentry',
            name='id',
            field=models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='المعرف الفريد'),
        ),
        migrations.AlterField(
            model_name='payrollperiod',
            name='id',
            field=models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='المعرف'),
        ),
        migrations.AlterModelTable(
            name='payrollentry',
            table='hrms_payroll_entry',
        ),
        migrations.AlterModelTable(
            name='payrollperiod',
            table=None,
        ),
        migrations.DeleteModel(
            name='EmployeeEmergencyContact',
        ),
        migrations.DeleteModel(
            name='EmployeeTraining',
        ),
    ]
